import { useState, useRef } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Play, Pause, SkipBack, SkipForward, Home } from 'lucide-react';
import videoSrc from '../assets/video.mp4';

interface SubTask {
  id: string;
  name: string;
  color: string;
  startTime: number;
  endTime: number;
  description: string;
  input: string;
  output: string;
  assertion: string;
}

const mockSubTasks: SubTask[] = [
  {
    id: '1',
    name: '打开浏览器',
    color: '#93c5fd',
    startTime: 0,
    endTime: 3.0,
    description: '启动Chrome浏览器并等待加载完成',
    input: '点击Chrome图标',
    output: 'Chrome浏览器窗口打开',
    assertion: '浏览器窗口可见且响应'
  },
  {
    id: '2',
    name: '导航到网站',
    color: '#86efac',
    startTime: 3.0,
    endTime: 6.0,
    description: '在地址栏输入京东搜索页面URL并访问',
    input: '输入search.jd.com',
    output: '京东搜索页面加载',
    assertion: '页面标题包含"京东"'
  },
  {
    id: '3',
    name: '搜索商品',
    color: '#fde68a',
    startTime: 6.0,
    endTime: 9.0,
    description: '在搜索框中输入"Mac电脑"并执行搜索',
    input: '搜索关键词: Mac电脑',
    output: '搜索结果页面显示',
    assertion: '搜索结果包含Mac相关商品'
  },
  {
    id: '4',
    name: '选择商品',
    color: '#fca5a5',
    startTime: 9.0,
    endTime: 12.0,
    description: '点击第一个商品进入详情页面',
    input: '点击商品容器',
    output: '商品详情页面打开',
    assertion: '页面显示商品详细信息'
  },
  {
    id: '5',
    name: '复制标题',
    color: '#c4b5fd',
    startTime: 12.0,
    endTime: 15.0,
    description: '复制商品标题到剪贴板',
    input: '选择商品标题文本',
    output: '标题复制到剪贴板',
    assertion: '剪贴板包含商品标题'
  }
];

export const EditPage = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(15);
  const [selectedTaskId, setSelectedTaskId] = useState(mockSubTasks[0].id);
  const videoRef = useRef<HTMLVideoElement>(null);

  const selectedTask = mockSubTasks.find(task => task.id === selectedTaskId) || mockSubTasks[0];

  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const seekTo = (time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const time = videoRef.current.currentTime;
      setCurrentTime(time);
      
      const activeTask = mockSubTasks.find(task => time >= task.startTime && time <= task.endTime);
      if (activeTask && activeTask.id !== selectedTaskId) {
        setSelectedTaskId(activeTask.id);
      }
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTaskBlockStyle = (task: SubTask) => {
    const left = (task.startTime / duration) * 100;
    const width = ((task.endTime - task.startTime) / duration) * 100;
    return {
      left: `${left}%`,
      width: `${width}%`,
      backgroundColor: task.color
    };
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* 顶部导航栏 */}
      <div className="bg-white/85 backdrop-blur-xl shadow-sm border-b border-gray-200/50 p-4">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-4">
            <Link
              to="/"
              className="flex items-center space-x-2 px-3 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-all duration-200 hover:scale-105 active:scale-95 text-sm font-medium shadow-sm"
            >
              <Home size={16} />
              <span>返回主页</span>
            </Link>
            <h1 className="text-xl font-semibold text-gray-900">
              任务编辑器
            </h1>
          </div>
          <div className="text-sm text-gray-500">
            视频时长: {formatTime(duration)}
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 视频播放器 */}
        <div className="bg-white/85 backdrop-blur-xl shadow-sm p-6 border-b border-gray-200/50">
          <div className="max-w-4xl mx-auto">
            <div className="relative bg-black rounded-2xl overflow-hidden shadow-lg ring-1 ring-gray-200/50">
              <video
                ref={videoRef}
                src={videoSrc}
                className="w-full h-96 object-contain"
                onTimeUpdate={handleTimeUpdate}
                onLoadedMetadata={handleLoadedMetadata}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
              />
              
              <div className="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-all duration-300 bg-black/20">
                <button
                  onClick={togglePlay}
                  className="bg-white/90 hover:bg-white rounded-full p-4 transition-all duration-200 hover:scale-110 active:scale-95 shadow-lg"
                >
                  {isPlaying ? (
                    <Pause size={32} className="text-gray-800" />
                  ) : (
                    <Play size={32} className="text-gray-800 ml-1" />
                  )}
                </button>
              </div>
            </div>

            {/* 播放控制栏 */}
            <div className="mt-6 flex items-center space-x-4 bg-white/50 backdrop-blur-sm rounded-xl p-4 ring-1 ring-gray-200/50">
              <button
                onClick={() => seekTo(Math.max(0, currentTime - 10))}
                className="p-2 rounded-lg hover:bg-purple-500/10 transition-all duration-200 hover:scale-110 active:scale-95"
              >
                <SkipBack size={20} className="text-gray-600 hover:text-purple-500 transition-colors" />
              </button>
              
              <button
                onClick={togglePlay}
                className="p-3 rounded-lg hover:bg-purple-500/10 transition-all duration-200 hover:scale-110 active:scale-95 bg-purple-500/5"
              >
                {isPlaying ? (
                  <Pause size={20} className="text-purple-600" />
                ) : (
                  <Play size={20} className="text-purple-600" />
                )}
              </button>
              
              <button
                onClick={() => seekTo(Math.min(duration, currentTime + 10))}
                className="p-2 rounded-lg hover:bg-purple-500/10 transition-all duration-200 hover:scale-110 active:scale-95"
              >
                <SkipForward size={20} className="text-gray-600 hover:text-purple-500 transition-colors" />
              </button>
              
              <div className="flex-1 flex items-center space-x-3">
                <span className="text-sm text-gray-600 min-w-[45px] font-medium">
                  {formatTime(currentTime)}
                </span>
                
                <div className="flex-1 relative">
                  <input
                    type="range"
                    min="0"
                    max={duration}
                    value={currentTime}
                    onChange={(e) => seekTo(Number(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                  />
                </div>
                
                <span className="text-sm text-gray-600 min-w-[45px] font-medium">
                  {formatTime(duration)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 时间轴和任务块 */}
        <div className="bg-white/85 backdrop-blur-xl shadow-sm p-4 border-b border-gray-200/50">
          <div className="max-w-7xl mx-auto">
            {/* 时间轴 */}
            <div className="relative h-16 bg-gray-100/50 rounded-xl overflow-hidden ring-1 ring-gray-200/50 shadow-sm">
              <div
                className="absolute top-0 bottom-0 w-1 bg-red-500 z-10 rounded-full shadow-sm"
                style={{ left: `${(currentTime / duration) * 100}%` }}
              />

              {mockSubTasks.map((task) => (
                <div
                  key={task.id}
                  className={`absolute top-2 bottom-2 rounded-lg cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-md ${
                    selectedTaskId === task.id ? 'ring-2 ring-white ring-offset-2 shadow-lg scale-105' : 'hover:opacity-90'
                  }`}
                  style={getTaskBlockStyle(task)}
                  onClick={() => {
                    setSelectedTaskId(task.id);
                    seekTo(task.startTime);
                  }}
                  title={task.name}
                />
              ))}
            </div>

            {/* 任务色块 - 紧贴时间轴下方 */}
            <div className="relative h-8 bg-gray-50/50 rounded-lg overflow-hidden ring-1 ring-gray-200/50 shadow-sm mt-1">
              {mockSubTasks.map((task) => (
                <div
                  key={`block-${task.id}`}
                  className={`absolute top-1 bottom-1 rounded cursor-pointer transition-all duration-200 hover:scale-105 ${
                    selectedTaskId === task.id ? 'ring-2 ring-white ring-offset-1 shadow-md scale-105' : 'hover:opacity-90'
                  }`}
                  style={getTaskBlockStyle(task)}
                  onClick={() => {
                    setSelectedTaskId(task.id);
                    seekTo(task.startTime);
                  }}
                  title={task.name}
                />
              ))}
            </div>

            {/* 任务按钮 */}
            <div className="mt-3 flex flex-wrap gap-2">
              {mockSubTasks.map((task) => (
                <button
                  key={task.id}
                  onClick={() => {
                    setSelectedTaskId(task.id);
                    seekTo(task.startTime);
                  }}
                  className={`px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 hover:scale-105 active:scale-95 ${
                    selectedTaskId === task.id
                      ? 'text-gray-800 shadow-lg ring-2 ring-gray-300 ring-offset-2'
                      : 'text-gray-700 bg-white/70 hover:bg-white shadow-sm ring-1 ring-gray-200/50'
                  }`}
                  style={{
                    backgroundColor: selectedTaskId === task.id ? task.color : undefined,
                    fontWeight: selectedTaskId === task.id ? '600' : '500'
                  }}
                >
                  {task.name}
                </button>
              ))}
            </div>

            {/* 任务信息容器 - 与时间轴同宽 */}
            <div className="mt-4 bg-white/70 rounded-xl p-4 ring-1 ring-gray-200/50 shadow-sm">
              <div className="flex">
                {/* 左侧面板 - 任务信息 */}
                <div className="w-1/2 pr-4 border-r border-gray-200/50">
                  <div className="space-y-4">
                    {/* 任务详情 - 紧凑版 */}
                    <div>
                      <div className="flex items-center mb-2">
                        <div
                          className="w-3 h-3 rounded-full mr-2 shadow-sm"
                          style={{ backgroundColor: selectedTask.color }}
                        />
                        <h4 className="text-sm font-semibold text-gray-900">{selectedTask.name}</h4>
                        <span className="ml-auto text-xs text-gray-500 bg-gray-100/70 px-2 py-1 rounded-full">
                          {formatTime(selectedTask.startTime)} - {formatTime(selectedTask.endTime)}
                        </span>
                      </div>
                      <p className="text-xs text-gray-600 leading-relaxed mb-3">{selectedTask.description}</p>
                    </div>

                    {/* 任务输入 - 紧凑版 */}
                    <div>
                      <h3 className="text-sm font-semibold text-gray-900 mb-2">任务输入</h3>
                      <div className="bg-white rounded-lg p-3 ring-1 ring-gray-200/50 shadow-sm">
                        <textarea
                          value={selectedTask.input}
                          readOnly
                          className="w-full h-12 bg-transparent border-none outline-none resize-none text-xs text-gray-700"
                          placeholder="任务输入信息..."
                        />
                      </div>
                    </div>

                    {/* 任务输出 - 紧凑版 */}
                    <div>
                      <h3 className="text-sm font-semibold text-gray-900 mb-2">任务输出</h3>
                      <div className="bg-white rounded-lg p-3 ring-1 ring-gray-200/50 shadow-sm">
                        <textarea
                          value={selectedTask.output}
                          readOnly
                          className="w-full h-12 bg-transparent border-none outline-none resize-none text-xs text-gray-700"
                          placeholder="任务输出信息..."
                        />
                      </div>
                    </div>

                    {/* 任务断言 - 紧凑版 */}
                    <div>
                      <h3 className="text-sm font-semibold text-gray-900 mb-2">任务断言</h3>
                      <div className="bg-white rounded-lg p-3 ring-1 ring-gray-200/50 shadow-sm">
                        <textarea
                          value={selectedTask.assertion}
                          readOnly
                          className="w-full h-12 bg-transparent border-none outline-none resize-none text-xs text-gray-700"
                          placeholder="任务断言信息..."
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* 右侧面板 - 空文本框区域 */}
                <div className="w-1/2 pl-4">
                  <div className="h-full">
                    <textarea
                      className="w-full h-full bg-white rounded-lg p-4 ring-1 ring-gray-200/50 shadow-sm border-none outline-none resize-none text-sm text-gray-700 min-h-[400px]"
                      placeholder="在此输入内容..."
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
