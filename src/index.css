/* System font optimization for better Mac compatibility */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for ACTOR interface */
.actor-window {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-white\/20::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thumb-white\/20::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thumb-white\/20::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.scrollbar-thumb-white\/20::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}

/* Hide scrollbar completely */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* 旋转动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 光标闪烁动画 */
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Mac window controls hover effects */
.mac-control {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.mac-control:hover {
  transform: scale(1.1);
}

.mac-control:active {
  transform: scale(0.95);
}

/* Custom cursor animation */
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.custom-cursor {
  animation: blink 0.8s infinite;
}

/* Button press animation */
@keyframes buttonPress {
  0% { transform: translateY(0px); }
  50% { transform: translateY(1px); }
  100% { transform: translateY(0px); }
}

.button-press {
  animation: buttonPress 0.2s ease-out;
}

/* Elastic zoom animation for icon */
@keyframes elasticZoom {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.elastic-zoom {
  animation: elasticZoom 0.3s ease-out;
}

/* Improved focus styles */
input:focus {
  caret-color: transparent;
}

/* Smooth transitions */
* {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar if needed */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(142, 68, 173, 0.3);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(142, 68, 173, 0.5);
}

/* System font optimization for Mac */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', system-ui, sans-serif;
  font-feature-settings: 'kern' 1, 'liga' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced window appearance */
.floating-window {
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
}